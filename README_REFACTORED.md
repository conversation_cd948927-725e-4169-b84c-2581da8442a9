# Telegram Media Downloader - FastAPI重构实现方案

## 概述

这是Telegram Media Downloader基于FastAPI的完整重构实现方案，提供了详细的前后端分离架构设计和具体实现步骤。项目已从Flask迁移到FastAPI，采用现代化的异步架构。

## 1. 前端界面实现清单

### 1.1 已实现的界面组件

| 界面 | 路径 | 文件位置 | 功能描述 | 状态 |
|------|------|----------|----------|------|
| 登录页面 | `/login` | `frontend/src/pages/Login/index.tsx` | 用户认证，支持用户名密码登录 | ✅ 完成 |
| 仪表板 | `/dashboard` | `frontend/src/pages/Dashboard/index.tsx` | 系统概览、任务统计、实时状态监控 | ✅ 完成 |
| 下载管理 | `/download` | `frontend/src/pages/Download/index.tsx` | 创建下载任务、任务列表、进度监控 | ✅ 完成 |
| 转发管理 | `/forward` | `frontend/src/pages/Forward/index.tsx` | 创建转发任务、转发规则配置 | ✅ 完成 |
| 监听转发 | `/listen` | `frontend/src/pages/Listen/index.tsx` | 实时监听转发、消息流展示 | ✅ 完成 |
| 系统设置 | `/settings` | `frontend/src/pages/Settings/index.tsx` | 完整的系统配置界面 | ✅ 完成 |

### 1.2 前端技术栈和目录结构

```
frontend/
├── src/
│   ├── pages/           # 页面组件
│   │   ├── Login/       # 登录页面
│   │   ├── Dashboard/   # 仪表板
│   │   ├── Download/    # 下载管理
│   │   ├── Forward/     # 转发管理
│   │   ├── Listen/      # 监听转发
│   │   └── Settings/    # 系统设置
│   ├── components/      # 通用组件
│   ├── services/        # API服务层
│   │   └── api.ts       # HTTP客户端配置
│   ├── store/           # 状态管理 (Zustand)
│   │   ├── authStore.ts     # 认证状态
│   │   ├── taskStore.ts     # 任务状态
│   │   └── settingsStore.ts # 设置状态
│   ├── types/           # TypeScript类型定义
│   │   └── index.ts     # 通用类型
│   └── router/          # 路由配置
│       └── index.tsx    # 路由定义
```

**技术栈**:
- React 18 + TypeScript
- Ant Design 5.x (UI组件库)
- Zustand (状态管理)
- Axios (HTTP客户端)
- Socket.IO Client (WebSocket通信)

## 2. FastAPI后端服务实现清单

### 2.1 FastAPI项目结构

```
fastapi_app/
├── __init__.py
├── config.py              # 配置管理 (Pydantic Settings)
├── database.py            # 数据库模型 (SQLAlchemy 2.0 异步)
├── auth.py               # JWT认证
├── utils.py              # 工具函数
├── websocket.py          # WebSocket管理
└── routers/              # API路由模块
    ├── __init__.py       # 路由注册
    ├── auth.py          # 认证路由
    ├── accounts.py      # 账号管理
    ├── groups.py        # 群组管理
    ├── tasks.py         # 任务管理
    ├── dashboard.py     # 仪表板
    └── settings.py      # 系统设置
```

### 2.2 核心服务模块

#### 2.2.1 FastAPI应用配置

**文件**: `fastapi_app/config.py` (已实现)
**技术**: Pydantic Settings + 环境变量
**实现内容**:

```python
class Settings(BaseSettings):
    """应用配置类"""
    # 应用基础配置
    app_name: str = "Telegram Media Downloader"
    debug: bool = False
    host: str = "127.0.0.1"
    port: int = 8000

    # 安全配置
    secret_key: str = "your-secret-key"
    access_token_expire_minutes: int = 30
    algorithm: str = "HS256"

    # CORS配置
    cors_origins: List[str] = ["http://localhost:3000"]
    trusted_hosts: List[str] = ["localhost", "127.0.0.1"]

    # 数据库配置
    database_url: str = "sqlite:///./telegram_downloader.db"
    database_echo: bool = False

    # Telegram配置
    telegram_api_id: Optional[int] = None
    telegram_api_hash: Optional[str] = None
    telegram_session_path: str = "./sessions"

    # 任务配置
    max_concurrent_downloads: int = 5
    download_timeout: int = 300
    download_path: str = "./downloads"

    class Config:
        env_file = ".env"
        case_sensitive = False
```

#### 2.2.2 数据库模型 (SQLAlchemy 2.0 异步)

**文件**: `fastapi_app/database.py` (已实现)
**技术**: SQLAlchemy 2.0 + 异步支持
**实现内容**:

```python
# 异步数据库引擎
engine = create_async_engine(database_url, echo=settings.database_echo)
async_session_maker = async_sessionmaker(engine, class_=AsyncSession)

class User(Base):
    """用户表"""
    __tablename__ = "users"
    id: Mapped[int] = mapped_column(Integer, primary_key=True)
    username: Mapped[str] = mapped_column(String(50), unique=True)
    hashed_password: Mapped[str] = mapped_column(String(255))
    role: Mapped[str] = mapped_column(String(20), default="user")
    is_active: Mapped[bool] = mapped_column(Boolean, default=True)

class TelegramAccount(Base):
    """Telegram账号表"""
    __tablename__ = "telegram_accounts"
    id: Mapped[int] = mapped_column(Integer, primary_key=True)
    account_id: Mapped[str] = mapped_column(String(50), unique=True)
    name: Mapped[str] = mapped_column(String(255))
    phone: Mapped[Optional[str]] = mapped_column(String(20))
    session_data: Mapped[Optional[str]] = mapped_column(Text)
    is_active: Mapped[bool] = mapped_column(Boolean, default=False)

class Task(Base):
    """任务表"""
    __tablename__ = "tasks"
    id: Mapped[int] = mapped_column(Integer, primary_key=True)
    task_id: Mapped[str] = mapped_column(String(50), unique=True)
    name: Mapped[str] = mapped_column(String(255))
    type: Mapped[str] = mapped_column(String(50))  # download, forward, etc.
    status: Mapped[str] = mapped_column(String(20), default="pending")
    progress: Mapped[int] = mapped_column(Integer, default=0)  # 0-100
    config: Mapped[Optional[dict]] = mapped_column(JSON)

# 异步数据库会话
async def get_db() -> AsyncGenerator[AsyncSession, None]:
    async with async_session_maker() as session:
        try:
            yield session
        except Exception:
            await session.rollback()
            raise
        finally:
            await session.close()
```

#### 2.2.3 API路由系统

**文件**: `fastapi_app/routers/` (已实现)
**技术**: FastAPI Router + Pydantic Models
**实现内容**:

```python
# 主路由注册 (fastapi_app/routers/__init__.py)
api_router = APIRouter()
api_router.include_router(auth_router, prefix="/auth", tags=["认证"])
api_router.include_router(accounts_router, prefix="/accounts", tags=["账号管理"])
api_router.include_router(groups_router, prefix="/groups", tags=["群组管理"])
api_router.include_router(tasks_router, prefix="/tasks", tags=["任务管理"])
api_router.include_router(dashboard_router, prefix="/dashboard", tags=["仪表板"])
api_router.include_router(settings_router, prefix="/settings", tags=["系统设置"])

# 任务管理路由 (fastapi_app/routers/tasks.py)
@router.get("")
async def get_tasks(
    page: int = Query(1, ge=1),
    limit: int = Query(20, ge=1, le=100),
    status: Optional[str] = Query(None),
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """获取任务列表 - 支持分页和过滤"""
    query = select(Task)
    if status:
        query = query.where(Task.status == status)

    # 分页查询
    offset = (page - 1) * limit
    query = query.offset(offset).limit(limit).order_by(Task.created_at.desc())
    result = await db.execute(query)
    tasks = result.scalars().all()

    return create_response(success=True, data=tasks)

# 认证路由 (fastapi_app/routers/auth.py)
@router.post("/login", response_model=LoginResponse)
async def login(login_data: LoginRequest, db: AsyncSession = Depends(get_db)):
    """用户登录 - JWT Token认证"""
    user = authenticate_user(login_data.username, login_data.password)
    if not user:
        raise HTTPException(status_code=401, detail="用户名或密码错误")

    token = create_user_token(user)
    return LoginResponse(success=True, data={"token": token.access_token})

# 设置路由 (fastapi_app/routers/settings.py)
@router.get("", response_model=SettingsResponse)
async def get_settings(
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """获取系统设置 - 集成原有Application配置"""
    app = get_app_instance()  # 获取原有Application实例
    settings = _convert_app_to_system_settings(app, config_manager)
    return create_response(success=True, data=settings.dict())
```

#### 2.2.4 WebSocket实时通信

**文件**: `fastapi_app/websocket.py` (已实现)
**技术**: FastAPI WebSocket + 连接管理器
**实现内容**:

```python
class ConnectionManager:
    """WebSocket连接管理器"""

    def __init__(self):
        self.active_connections: Dict[str, WebSocket] = {}
        self.user_connections: Dict[str, Set[str]] = {}

    async def connect(self, websocket: WebSocket, connection_id: str, user_id: str = None):
        """接受WebSocket连接"""
        await websocket.accept()
        self.active_connections[connection_id] = websocket

    async def send_personal_message(self, message: dict, connection_id: str):
        """发送个人消息"""
        if connection_id in self.active_connections:
            websocket = self.active_connections[connection_id]
            await websocket.send_text(json.dumps(message))

    async def broadcast(self, message: dict):
        """广播消息到所有连接"""
        for connection_id in list(self.active_connections.keys()):
            await self.send_personal_message(message, connection_id)

# WebSocket端点
@websocket_router.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket):
    """WebSocket端点"""
    connection_id = f"conn_{datetime.utcnow().timestamp()}"
    await manager.connect(websocket, connection_id)

    try:
        while True:
            data = await websocket.receive_text()
            message = json.loads(data)
            # 处理客户端消息
            await handle_websocket_message(message, connection_id)
    except WebSocketDisconnect:
        manager.disconnect(connection_id)

# 消息类型定义
def create_message(event: str, data: dict = None, message: str = None) -> dict:
    """创建WebSocket消息"""
    return {
        "event": event,
        "data": data or {},
        "message": message,
        "timestamp": datetime.utcnow().isoformat() + "Z"
    }
```

#### 2.2.5 主应用启动

**文件**: `main.py` (已实现)
**技术**: FastAPI + Uvicorn + 生命周期管理
**实现内容**:

```python
@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    # 启动时执行
    settings = get_settings()
    setup_logging(settings.log_level)

    # 初始化数据库
    await init_db()

    logging.info("🚀 FastAPI应用启动完成")

    yield

    # 关闭时执行
    await close_db()
    logging.info("👋 FastAPI应用关闭完成")

def create_app() -> FastAPI:
    """创建FastAPI应用实例"""
    settings = get_settings()

    app = FastAPI(
        title="Telegram Media Downloader API",
        description="Telegram媒体下载器的FastAPI后端服务",
        version="2.0.0",
        docs_url="/docs" if settings.debug else None,
        redoc_url="/redoc" if settings.debug else None,
        lifespan=lifespan
    )

    # 添加CORS中间件
    app.add_middleware(
        CORSMiddleware,
        allow_origins=settings.cors_origins,
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )

    # 注册路由
    app.include_router(api_router, prefix="/api")
    app.include_router(websocket_router)

    # 静态文件服务 - 服务前端应用
    frontend_dist = Path(__file__).parent / "frontend" / "dist"
    if frontend_dist.exists():
        app.mount("/static", StaticFiles(directory=str(frontend_dist)))

    return app

# 创建应用实例
app = create_app()
```

#### 2.1.5 WebSocket实时通信完善

**文件**: `module/websocket_server.py` (修改现有文件)
**实现内容**:

```python
# 添加新的消息类型
MESSAGE_TYPES = {
    'task_update': 'task_update',           # 任务状态更新
    'task_progress': 'task_progress',       # 任务进度更新
    'task_deleted': 'task_deleted',         # 任务删除通知
    'system_status': 'system_status',       # 系统状态
    'error_notification': 'error_notification',  # 错误通知
    'account_status': 'account_status',     # 账号状态变化
}

class WebSocketServer:
    async def broadcast_task_update(self, task_data: dict):
        """广播任务更新"""
        await self.broadcast({
            'type': 'task_update',
            'data': task_data
        })

    async def broadcast_task_progress(self, task_id: str, progress: float):
        """广播任务进度"""
        await self.broadcast({
            'type': 'task_progress',
            'data': {
                'taskId': task_id,
                'progress': progress
            }
        })

    async def broadcast_system_status(self, status_data: dict):
        """广播系统状态"""
        await self.broadcast({
            'type': 'system_status',
            'data': status_data
        })
```

### 2.3 与原有系统的集成

#### 2.3.1 保持兼容性

**设置管理集成** (`fastapi_app/routers/settings.py`):
```python
def get_app_instance():
    """获取原有Application实例"""
    global app_instance
    if app_instance is None:
        app_instance = Application(
            config_file="config.yaml",
            app_data_file="app_data.yaml",
            application_name="TelegramMediaDownloader"
        )
        app_instance.load_config()
    return app_instance

def _convert_app_to_system_settings(app: Application, config: Config) -> SystemSetstart_fastapitings:
    """将原有Application配置转换为FastAPI设置格式"""
    return SystemSettings(start_fastapi
        downloadPath=app.download_path,
        maxConcurrentTasks=app.max_concurrent_downloads,
        telegramSettings=TelegramSettings(
            apiId=app.api_id,
            apiHash=app.api_hash,
            sessionPath=app.session_file_path
        ),
        # ... 其他设置转换
    )
```

**任务管理集成** (需要实现):
```python
# 基于现有 module/bot.py 和 module/app.py 的逻辑
class TaskManager:
    """统一任务管理器 - 集成原有下载逻辑"""

    def __init__(self, app: Application):
        self.app = app
        self.download_bot = DownloadBot()  # 使用现有的DownloadBot

    async def create_download_task(self, task_data: dict) -> str:
        """创建下载任务 - 参考 module/bot.py"""
        # 使用现有的 ChatDownloadConfig
        config = ChatDownloadConfig(
            chat_id=int(task_data['sourceGroupId']),
            last_read_message_id=task_data.get('startMessageId', 0),
            end_message_id=task_data.get('endMessageId'),
            # ... 其他配置
        )

        # 集成现有的下载逻辑
        return await self._execute_download_with_bot(config)
```

#### 2.2.2 Web模块更新

**文件**: `module/web.py` (修改现有文件)
**主要改动**:

```python
# 添加导入
from module.database import init_db
from module.task_manager import init_task_manager
from module.telegram_client_manager import init_client_manager

# 配置数据库
_flask_app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///telegram_downloader.db'
_flask_app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

# 初始化数据库
init_db(_flask_app)

# 在init_web函数中添加
def init_web(app: Application):
    global web_login_users
    if app.web_login_secret:
        web_login_users = {"root": app.web_login_secret}
    else:
        _flask_app.config["LOGIN_DISABLED"] = True

    # 新增: 初始化客户端管理器
    client_manager = init_client_manager(app)

    # 新增: 初始化任务管理器
    task_manager = init_task_manager(app)

    # 新增: 异步初始化
    with _flask_app.app_context():
        import asyncio
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        loop.run_until_complete(task_manager.initialize())

    # 原有启动逻辑...
```

## 3. FastAPI前后端集成方案

### 3.1 启动方式

#### 3.1.1 FastAPI服务启动

项目提供了三种不同的启动方式，适用于不同的使用场景：

**方式一: 完整启动脚本 (推荐)**
```bash
# 开发模式 - 自动重载
python run_fastapi_server.py --reload --log-level debug

# 生产模式 - 多进程
python run_fastapi_server.py --workers 4 --log-level warning

# HTTPS模式
python run_fastapi_server.py --ssl-certfile cert.pem --ssl-keyfile key.pem

# 自定义配置
python run_fastapi_server.py --host 0.0.0.0 --port 9000 --reload
```

**方式二: 简化启动脚本 (快速测试)**
```bash
# 快速开发测试 (固定127.0.0.1:8000)
python start_fastapi.py
```

**方式三: 直接使用uvicorn (高级用户)**
```bash
# 开发模式
uvicorn main:app --host 127.0.0.1 --port 8000 --reload

# 生产模式
uvicorn main:app --host 0.0.0.0 --port 8000 --workers 4
```

#### 3.1.2 启动脚本功能对比

| 功能特性 | start_fastapi.py | run_fastapi_server.py |
|----------|------------------|----------------------|
| **适用场景** | 快速测试 | 开发+生产 |
| **配置来源** | 硬编码 | 配置文件+命令行 |
| **命令行参数** | ❌ | ✅ |
| **自动重载** | ✅ 固定开启 | ✅ 可选 |
| **多进程支持** | ❌ | ✅ |
| **SSL/HTTPS** | ❌ | ✅ |
| **性能优化** | ❌ | ✅ (uvloop) |
| **详细日志** | ✅ | ✅ |
| **生产环境** | ❌ | ✅ 推荐 |

#### 3.1.3 使用建议

**开发阶段**:
```bash
# 快速开发测试
python start_fastapi.py

# 带调试的开发
python run_fastapi_server.py --reload --log-level debug --access-log
```

**测试部署**:
```bash
# 模拟生产环境
python run_fastapi_server.py --workers 2 --log-level info
```

**生产部署**:
```bash
# 标准生产配置
python run_fastapi_server.py --workers 4 --log-level warning

# HTTPS生产配置
python run_fastapi_server.py \
  --workers 4 \
  --ssl-certfile /path/to/cert.pem \
  --ssl-keyfile /path/to/key.pem \
  --host 0.0.0.0 \
  --port 443
```

#### 3.1.2 环境配置

**环境变量文件** (`.env`):
```env
# FastAPI应用配置
APP_NAME=Telegram Media Downloader
DEBUG=true
HOST=127.0.0.1
PORT=8000

# 安全配置
SECRET_KEY=your-secret-key-change-in-production
ACCESS_TOKEN_EXPIRE_MINUTES=30

# CORS配置
CORS_ORIGINS=http://localhost:3000,http://127.0.0.1:3000

# 数据库配置
DATABASE_URL=sqlite:///./telegram_downloader.db

# Telegram配置
TELEGRAM_API_ID=your_api_id
TELEGRAM_API_HASH=your_api_hash
TELEGRAM_SESSION_PATH=./sessions
```

### 3.2 API调用模式

**参考**: `frontend/src/store/settingsStore.ts` 的实现
**FastAPI标准模式**:

```typescript
// FastAPI标准响应格式
interface APIResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  timestamp: string;
}

// 配置API基础URL
const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://127.0.0.1:8000/api';

// Axios实例配置
const api = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// 请求拦截器 - 添加JWT Token
api.interceptors.request.use((config) => {
  const token = localStorage.getItem('access_token');
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});

// 响应拦截器 - 处理FastAPI响应格式
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      // Token过期，跳转到登录页
      localStorage.removeItem('access_token');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

// FastAPI任务管理API调用
const taskAPI = {
  // 获取任务列表 (支持分页)
  getTasks: async (page = 1, limit = 20, status?: string) => {
    const params = new URLSearchParams();
    params.append('page', page.toString());
    params.append('limit', limit.toString());
    if (status) params.append('status', status);

    const response = await api.get(`/tasks?${params}`);
    return response.data;
  },

  // 创建任务
  createTask: async (taskData: TaskCreateRequest) => {
    const response = await api.post('/tasks', taskData);
    return response.data;
  },

  // 获取任务详情
  getTask: async (taskId: string) => {
    const response = await api.get(`/tasks/${taskId}`);
    return response.data;
  },

  // 更新任务状态
  updateTask: async (taskId: string, updates: TaskUpdateRequest) => {
    const response = await api.put(`/tasks/${taskId}`, updates);
    return response.data;
  },

  // 删除任务
  deleteTask: async (taskId: string) => {
    const response = await api.delete(`/tasks/${taskId}`);
    return response.data;
  }
};

// 认证API
const authAPI = {
  login: async (username: string, password: string) => {
    const response = await api.post('/auth/login', { username, password });
    if (response.data.success) {
      localStorage.setItem('access_token', response.data.data.token);
    }
    return response.data;
  },

  logout: async () => {
    await api.post('/auth/logout');
    localStorage.removeItem('access_token');
  },

  getProfile: async () => {
    const response = await api.get('/auth/me');
    return response.data;
  }
};
```

### 3.2 WebSocket集成

**实现位置**: `frontend/src/services/websocket.ts` (新建)
**功能**:

```typescript
import { io, Socket } from 'socket.io-client';
import { taskStore } from '../store/taskStore';
import { systemStore } from '../store/systemStore';

class WebSocketService {
  private socket: Socket | null = null;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;

  connect() {
    const WS_URL = process.env.REACT_APP_WS_URL || 'http://localhost:5000';
    this.socket = io(WS_URL);

    // 连接成功
    this.socket.on('connect', () => {
      console.log('WebSocket连接成功');
      this.reconnectAttempts = 0;
    });

    // 监听任务更新
    this.socket.on('task_update', (data) => {
      taskStore.getState().updateTaskFromWebSocket(data);
    });

    // 监听任务进度更新
    this.socket.on('task_progress', (data) => {
      taskStore.getState().updateProgressFromWebSocket(
        data.taskId,
        data.progress
      );
    });

    // 监听任务删除
    this.socket.on('task_deleted', (data) => {
      taskStore.getState().removeTaskFromWebSocket(data.id);
    });

    // 监听系统状态
    this.socket.on('system_status', (data) => {
      systemStore.getState().updateStatus(data);
    });

    // 监听错误通知
    this.socket.on('error_notification', (data) => {
      // 显示错误通知
      console.error('系统错误:', data.message);
    });

    // 连接断开处理
    this.socket.on('disconnect', () => {
      console.log('WebSocket连接断开');
      this.handleReconnect();
    });
  }

  private handleReconnect() {
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++;
      setTimeout(() => {
        console.log(`尝试重连 (${this.reconnectAttempts}/${this.maxReconnectAttempts})`);
        this.connect();
      }, 3000 * this.reconnectAttempts);
    }
  }

  disconnect() {
    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
    }
  }
}

export const webSocketService = new WebSocketService();
```

### 3.3 前端状态管理

**任务状态管理** (`frontend/src/store/taskStore.ts`):

```typescript
interface TaskStore {
  tasks: Task[];
  currentTask: Task | null;
  loading: boolean;

  // API操作方法
  fetchTasks: () => Promise<void>;
  createTask: (data: TaskCreateRequest) => Promise<string>;
  updateTask: (id: string, data: TaskUpdateRequest) => Promise<void>;
  deleteTask: (id: string) => Promise<void>;

  // WebSocket实时更新方法
  updateTaskFromWebSocket: (task: Task) => void;
  updateProgressFromWebSocket: (taskId: string, progress: number) => void;
  removeTaskFromWebSocket: (taskId: string) => void;
}

const useTaskStore = create<TaskStore>((set, get) => ({
  tasks: [],
  currentTask: null,
  loading: false,

  fetchTasks: async () => {
    set({ loading: true });
    try {
      const tasks = await taskAPI.getTasks();
      set({ tasks, loading: false });
    } catch (error) {
      set({ loading: false });
      throw error;
    }
  },

  createTask: async (data: TaskCreateRequest) => {
    const task = await taskAPI.createTask(data);
    set(state => ({ tasks: [...state.tasks, task] }));
    return task.id;
  },

  updateTaskFromWebSocket: (updatedTask: Task) => {
    set(state => ({
      tasks: state.tasks.map(task =>
        task.id === updatedTask.id ? updatedTask : task
      )
    }));
  },

  updateProgressFromWebSocket: (taskId: string, progress: number) => {
    set(state => ({
      tasks: state.tasks.map(task =>
        task.id === taskId ? { ...task, progress } : task
      )
    }));
  }
}));
```

## 4. 数据流和架构图

### 4.1 系统架构图

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端 (React)   │    │  API服务层      │    │  核心服务层      │
│                 │    │                 │    │                 │
│ ┌─────────────┐ │    │ ┌─────────────┐ │    │ ┌─────────────┐ │
│ │ 页面组件    │ │◄──►│ │ Flask路由   │ │◄──►│ │ 任务管理器  │ │
│ └─────────────┘ │    │ └─────────────┘ │    │ └─────────────┘ │
│ ┌─────────────┐ │    │ ┌─────────────┐ │    │ ┌─────────────┐ │
│ │ 状态管理    │ │◄──►│ │ 认证中间件  │ │    │ │ 客户端管理  │ │
│ └─────────────┘ │    │ └─────────────┘ │    │ └─────────────┘ │
│ ┌─────────────┐ │    │ ┌─────────────┐ │    │ ┌─────────────┐ │
│ │ WebSocket   │ │◄──►│ │ WebSocket   │ │◄──►│ │ 数据库管理  │ │
│ │ 客户端      │ │    │ │ 服务器      │ │    │ └─────────────┘ │
│ └─────────────┘ │    │ └─────────────┘ │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
        │                        │                        │
        │                        │                        │
        ▼                        ▼                        ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   HTTP/HTTPS    │    │   SQLite数据库   │    │ Telegram API    │
│   WebSocket     │    │                 │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 4.2 数据流图

```
用户操作 → 前端组件 → API调用 → 后端路由 → 服务层 → 数据库
                                    ↓
WebSocket推送 ← 任务管理器 ← 异步任务执行 ← Telegram客户端
```

## 5. 部署和运行指南

### 5.1 环境准备

```bash
# 1. Python环境 (Python 3.8+)
pip install -r requirements.txt

# 2. 前端环境 (Node.js 16+)
cd frontend
npm install
npm run build

# 3. 创建必要目录
mkdir -p downloads sessions logs
```

### 5.2 配置文件设置

**config.yaml** 示例:

```yaml
# Telegram API配置
api_id: 你的API_ID
api_hash: "你的API_HASH"

# Web服务配置
web_host: "0.0.0.0"
web_port: 5000
web_login_secret: "admin"  # Web登录密码
debug_web: false

# 数据库配置
database_url: "sqlite:///telegram_downloader.db"

# 下载配置
download_path: "./downloads"
max_concurrent_tasks: 3

# 日志配置
log_level: "INFO"
log_file: "telegram_downloader.log"
```

### 5.3 启动方式

```bash
# 方式一: 使用新的主服务器 (推荐)
python main_server.py

# 方式二: 使用原有方式 (兼容)
python media_downloader.py
```

### 5.4 快速开始

#### 5.4.1 推荐启动方式

```bash
# 1. 开发环境 - 推荐使用 (功能完整)
python run_fastapi_server.py --reload --log-level debug

# 2. 快速测试 - 最简单 (一键启动)
python start_fastapi.py

# 3. 生产环境 - 性能最佳 (多进程)
python run_fastapi_server.py --workers 4 --log-level warning
```

#### 5.4.2 访问界面

- **FastAPI Web界面**: http://127.0.0.1:8000
- **API文档**: http://127.0.0.1:8000/docs (Swagger UI)
- **ReDoc文档**: http://127.0.0.1:8000/redoc
- **WebSocket端点**: ws://127.0.0.1:8000/ws
- **默认登录**: 用户名 `admin`，密码 `admin`

#### 5.4.3 启动脚本选择指南

| 使用场景 | 推荐脚本 | 命令示例 |
|----------|----------|----------|
| **日常开发** | `run_fastapi_server.py` | `python run_fastapi_server.py --reload` |
| **快速测试** | `start_fastapi.py` | `python start_fastapi.py` |
| **功能验证** | `main.py` | `python main.py` |
| **生产部署** | `run_fastapi_server.py` | `python run_fastapi_server.py --workers 4` |
| **HTTPS部署** | `run_fastapi_server.py` | `python run_fastapi_server.py --ssl-certfile cert.pem` |

## 6. 测试和验证

### 6.1 功能测试清单

#### 基础功能测试
- [ ] 用户登录认证
- [ ] 系统设置保存和读取
- [ ] 账号添加和管理
- [ ] 群组列表获取和同步

#### 任务功能测试
- [ ] 下载任务创建和执行
- [ ] 转发任务创建和执行
- [ ] 监听转发任务创建和执行
- [ ] 任务暂停和恢复
- [ ] 任务停止和删除

#### 实时功能测试
- [ ] 任务进度实时更新
- [ ] WebSocket连接和断线重连
- [ ] 系统状态实时显示
- [ ] 错误通知推送

### 6.2 FastAPI测试示例

#### 6.2.1 启动服务进行测试

```bash
# 启动开发服务器
python run_fastapi_server.py --reload --log-level debug

# 或者快速启动
python start_fastapi.py
```

#### 6.2.2 API接口测试

```bash
# 1. 登录测试
curl -X POST http://127.0.0.1:8000/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username": "admin", "password": "admin"}'

# 2. 获取任务列表 (支持分页)
curl -X GET "http://127.0.0.1:8000/api/tasks?page=1&limit=20&status=pending" \
  -H "Authorization: Bearer <token>"

# 3. 创建下载任务
curl -X POST http://127.0.0.1:8000/api/tasks \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <token>" \
  -d '{
    "name": "下载任务1",
    "type": "download",
    "config": {
      "accountId": "account_id",
      "sourceGroupId": "group_id",
      "downloadPath": "./downloads",
      "fileTypes": ["photo", "video", "document"]
    }
  }'

# 4. 获取系统设置
curl -X GET http://127.0.0.1:8000/api/settings \
  -H "Authorization: Bearer <token>"

# 5. 健康检查
curl -X GET http://127.0.0.1:8000/health

# 6. 获取API文档 (浏览器访问)
# http://127.0.0.1:8000/docs

# 7. WebSocket连接测试 (使用wscat)
wscat -c ws://127.0.0.1:8000/ws
```

#### 6.2.3 不同启动方式的测试地址

| 启动方式 | 默认地址 | API文档 | WebSocket |
|----------|----------|---------|-----------|
| `start_fastapi.py` | http://127.0.0.1:8000 | /docs | ws://127.0.0.1:8000/ws |
| `main.py` | 根据配置文件 | /docs | ws://配置地址/ws |
| `run_fastapi_server.py` | 根据参数/配置 | /docs | ws://指定地址/ws |

## 7. 故障排除

### 7.1 常见问题

#### 数据库相关
- **问题**: 数据库初始化失败
- **解决**: 删除 `telegram_downloader.db` 文件，重新启动服务

#### 会话相关
- **问题**: Telegram会话失效
- **解决**: 删除 `sessions/` 目录下的会话文件，重新添加账号

#### 端口冲突
- **问题**: 端口被占用
- **解决**: 修改 `config.yaml` 中的 `web_port` 配置

#### 前端无法访问后端
- **问题**: CORS错误或API调用失败
- **解决**: 检查 `frontend/src/services/api.ts` 中的 `baseURL` 配置

### 7.2 日志查看

```bash
# 查看应用日志
tail -f telegram_downloader.log

# 查看任务执行详情
# 在Web界面的任务列表中点击任务查看详细日志
```

## 8. 扩展开发指南

### 8.1 添加新的任务类型

1. 在 `module/task_manager.py` 中添加新的 `TaskType`
2. 实现对应的 `_handle_xxx_task` 方法
3. 在前端添加对应的创建界面
4. 更新API接口和数据库模型

### 8.2 添加新的API接口

1. 在 `module/api.py` 中添加新的路由
2. 实现对应的业务逻辑
3. 在前端 `services/api.ts` 中添加调用方法
4. 更新前端组件使用新接口

### 8.3 添加新的前端页面

1. 在 `frontend/src/pages/` 中创建新的页面组件
2. 在 `frontend/src/router/index.tsx` 中添加路由
3. 在导航菜单中添加入口
4. 创建对应的状态管理store

## 9. FastAPI重构总结

本FastAPI重构方案提供了现代化的异步架构，主要改进包括：

### 9.1 技术升级

1. **FastAPI框架**: 从Flask迁移到FastAPI，获得更好的性能和开发体验
2. **异步支持**: 全面采用async/await，提升并发处理能力
3. **自动API文档**: 内置Swagger UI和ReDoc文档生成
4. **类型安全**: 基于Pydantic的请求/响应模型验证
5. **现代化配置**: 使用环境变量和Pydantic Settings

### 9.2 架构优势

1. **统一架构**: 将原本分散的功能整合到FastAPI统一架构中
2. **数据持久化**: SQLAlchemy 2.0异步ORM，支持多种数据库
3. **实时通信**: FastAPI原生WebSocket支持，性能更优
4. **多账号支持**: 完整的Telegram多账号管理和会话恢复
5. **API标准化**: RESTful API + OpenAPI规范，便于集成
6. **模块化设计**: 清晰的路由模块划分，便于维护

### 9.3 兼容性保持

1. **配置兼容**: 保持与原有config.yaml的兼容性
2. **功能兼容**: 集成原有的下载、转发、监听功能
3. **数据兼容**: 支持原有数据的迁移和转换
4. **渐进迁移**: 可以与原有Flask版本并行运行

### 9.4 开发体验

1. **自动重载**: 开发模式下支持代码热重载
2. **类型提示**: 完整的TypeScript类型支持
3. **错误处理**: 统一的异常处理和错误响应
4. **日志系统**: 结构化日志记录和监控

### 9.5 部署优势

1. **容器化**: 更好的Docker支持和容器化部署
2. **性能监控**: 内置性能指标和健康检查
3. **扩展性**: 支持水平扩展和负载均衡
4. **安全性**: JWT认证、CORS配置、请求验证

通过这个FastAPI重构方案，系统获得了现代化的技术栈、更好的性能表现、完整的API文档，同时保持了与原有系统的兼容性，为后续的功能扩展和维护提供了坚实的基础。
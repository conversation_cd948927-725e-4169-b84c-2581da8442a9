#!/usr/bin/env python3
"""
FastAPI服务器启动脚本
用于开发和生产环境
"""

import os
import sys
import argparse
import uvicorn
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from fastapi_app.config import get_settings


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="启动Telegram Media Downloader FastAPI服务器")
    
    parser.add_argument(
        "--host",
        default=None,
        help="服务器主机地址 (默认: 127.0.0.1)"
    )
    
    parser.add_argument(
        "--port",
        type=int,
        default=None,
        help="服务器端口 (默认: 8000)"
    )
    
    parser.add_argument(
        "--reload",
        action="store_true",
        help="启用自动重载 (开发模式)"
    )
    
    parser.add_argument(
        "--workers",
        type=int,
        default=1,
        help="工作进程数 (生产模式)"
    )
    
    parser.add_argument(
        "--log-level",
        choices=["critical", "error", "warning", "info", "debug", "trace"],
        default=None,
        help="日志级别"
    )
    
    parser.add_argument(
        "--access-log",
        action="store_true",
        help="启用访问日志"
    )
    
    parser.add_argument(
        "--ssl-keyfile",
        help="SSL私钥文件路径"
    )
    
    parser.add_argument(
        "--ssl-certfile",
        help="SSL证书文件路径"
    )
    
    args = parser.parse_args()
    
    # 获取配置
    settings = get_settings()
    
    # 使用命令行参数覆盖配置
    host = args.host or settings.host
    port = args.port or settings.port
    reload = args.reload or settings.debug
    log_level = args.log_level or ("debug" if settings.debug else "info")
    access_log = args.access_log or settings.debug
    
    # 打印启动信息
    print("🚀 启动 Telegram Media Downloader FastAPI 服务器")
    print("=" * 60)
    print(f"📍 地址: {'https' if args.ssl_certfile else 'http'}://{host}:{port}")
    print(f"🔧 重载模式: {reload}")
    print(f"📊 日志级别: {log_level}")
    print(f"📝 访问日志: {access_log}")
    print(f"🌐 CORS允许: {settings.cors_origins}")
    
    if settings.debug:
        print(f"📚 API文档: http://{host}:{port}/docs")
        print(f"📖 ReDoc文档: http://{host}:{port}/redoc")
    
    if args.ssl_certfile:
        print(f"🔒 SSL证书: {args.ssl_certfile}")
        print(f"🔑 SSL私钥: {args.ssl_keyfile}")
    
    print("=" * 60)
    
    # 配置uvicorn参数
    uvicorn_config = {
        "app": "main:app",
        "host": host,
        "port": port,
        "log_level": log_level,
        "access_log": access_log,
    }
    
    # 开发模式配置
    if reload:
        uvicorn_config.update({
            "reload": True,
            "reload_dirs": [str(Path(__file__).parent)],
            "reload_excludes": ["*.pyc", "*.pyo", "__pycache__", ".git", ".env"]
        })
    else:
        # 生产模式配置
        production_config = {
            "workers": args.workers,
        }

        # 仅在非Windows系统上使用uvloop和httptools
        if sys.platform != "win32":
            production_config.update({
                "loop": "uvloop",  # 使用uvloop提高性能
                "http": "httptools"  # 使用httptools提高性能
            })

        uvicorn_config.update(production_config)
    
    # SSL配置
    if args.ssl_certfile and args.ssl_keyfile:
        uvicorn_config.update({
            "ssl_certfile": args.ssl_certfile,
            "ssl_keyfile": args.ssl_keyfile
        })
    
    try:
        # 启动服务器
        uvicorn.run(**uvicorn_config)
    except KeyboardInterrupt:
        print("\n👋 服务器已停止")
    except Exception as e:
        print(f"❌ 服务器启动失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
